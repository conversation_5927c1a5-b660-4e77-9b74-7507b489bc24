<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\reactapp.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:51049</SpaProxyServerUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.*-*</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="2.17.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="2.17.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\reactapp.client\reactapp.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>

</Project>
