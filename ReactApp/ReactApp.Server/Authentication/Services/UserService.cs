using ReactApp.Server.Authentication.Interfaces;
using ReactApp.Server.Authentication.Models;

namespace ReactApp.Server.Authentication.Services
{
    public class UserService : IUserService
    {
        private readonly ITokenValidator _tokenValidator;

        public UserService(ITokenValidator tokenValidator)
        {
            _tokenValidator = tokenValidator;
        }

        public async Task<UserInfo> GetUserInfoAsync(string userId)
        {
            // In a real application, you might fetch additional user information from a database
            // or from Microsoft Graph API using the userId
            
            return new UserInfo
            {
                Id = userId,
                DisplayName = "User " + userId,
                Email = $"user{userId}@example.com",
                Roles = new List<string> { "User" }
            };
        }

        public async Task<bool> IsUserAuthenticatedAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            return await _tokenValidator.ValidateTokenAsync(token);
        }
    }
}