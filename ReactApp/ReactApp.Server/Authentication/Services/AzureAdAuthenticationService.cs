using Microsoft.Identity.Web;
using ReactApp.Server.Authentication.Interfaces;
using ReactApp.Server.Authentication.Models;

namespace ReactApp.Server.Authentication.Services
{
    public class AzureAdAuthenticationService : IAuthenticationService
    {
        private readonly ITokenAcquisition _tokenAcquisition;
        private readonly ITokenValidator _tokenValidator;
        private readonly IConfiguration _configuration;

        public AzureAdAuthenticationService(
            ITokenAcquisition tokenAcquisition,
            ITokenValidator tokenValidator,
            IConfiguration configuration)
        {
            _tokenAcquisition = tokenAcquisition;
            _tokenValidator = tokenValidator;
            _configuration = configuration;
        }

        public async Task<AuthenticationResult> ValidateTokenAsync(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = "Token is empty" };
            }

            try
            {
                bool isValid = await _tokenValidator.ValidateTokenAsync(token);
                
                if (!isValid)
                {
                    return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = "Invalid token" };
                }

                var claims = await _tokenValidator.GetClaimsFromTokenAsync(token);
                string userId = claims.ContainsKey("oid") ? claims["oid"] : string.Empty;
                string userName = claims.ContainsKey("name") ? claims["name"] : string.Empty;

                return new AuthenticationResult
                {
                    IsAuthenticated = true,
                    UserId = userId,
                    UserName = userName,
                    Token = token,
                    Claims = claims
                };
            }
            catch (Exception ex)
            {
                return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = ex.Message };
            }
        }

        public string GetLoginUrl(string redirectUri)
        {
            var tenantId = _configuration["AzureAd:TenantId"];
            var clientId = _configuration["AzureAd:ClientId"];
            var instance = _configuration["AzureAd:Instance"];
            
            return $"{instance}{tenantId}/oauth2/v2.0/authorize?client_id={clientId}&response_type=code&redirect_uri={Uri.EscapeDataString(redirectUri)}&response_mode=query&scope=openid%20profile%20email";
        }

        public async Task<string> GetAccessTokenForUserAsync(string userId, string[] scopes)
        {
            try
            {
                return await _tokenAcquisition.GetAccessTokenForUserAsync(scopes);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
    }
}