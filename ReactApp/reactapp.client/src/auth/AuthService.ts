import { AuthenticationResult } from "@azure/msal-browser";

export interface UserInfo {
  id: string;
  displayName: string;
  email: string;
  roles: string[];
}

export class AuthService {
  private baseUrl: string = "";

  constructor() {
    this.baseUrl = window.location.origin;
  }

  async validateToken(token: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/validate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        throw new Error("Token validation failed");
      }

      return await response.json();
    } catch (error) {
      console.error("Error validating token:", error);
      throw error;
    }
  }

  async getUserInfo(token: string): Promise<UserInfo> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/user`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to get user info");
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting user info:", error);
      throw error;
    }
  }

  async handleLoginSuccess(result: AuthenticationResult): Promise<UserInfo | null> {
    try {
      const token = result.accessToken;
      const validationResult = await this.validateToken(token);
      
      if (!validationResult.isAuthenticated) {
        throw new Error(validationResult.errorMessage || "Authentication failed");
      }
      
      return await this.getUserInfo(token);
    } catch (error) {
      console.error("Login processing error:", error);
      return null;
    }
  }
}

export const authService = new AuthService();