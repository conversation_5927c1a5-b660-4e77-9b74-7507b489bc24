// @ts-ignore
import { ReactNode, useEffect, useState } from "react";
import { AuthContext } from "./AuthContext";
import { PublicClientApplication, AuthenticationResult } from "@azure/msal-browser";
import { msalConfig, loginRequest } from "./MsalConfig";
import { authService, UserInfo } from "./AuthService";

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [msalInstance] = useState(() => new PublicClientApplication(msalConfig));

  useEffect(() => {
    const initializeAuth = async () => {
      await msalInstance.initialize();
      
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        msalInstance.setActiveAccount(accounts[0]);
        
        try {
          const tokenResponse = await msalInstance.acquireTokenSilent({
            ...loginRequest,
            account: accounts[0],
          });
          
          const userInfo = await authService.handleLoginSuccess(tokenResponse);
          if (userInfo) {
            setUser(userInfo);
            setIsAuthenticated(true);
          }
        } catch (error) {
          console.error("Silent token acquisition failed", error);
        }
      }
      
      setIsLoading(false);
    };

    initializeAuth();
  }, [msalInstance]);

  const login = async () => {
    try {
      setIsLoading(true);
      const result = await msalInstance.loginPopup(loginRequest);
      
      if (result) {
        msalInstance.setActiveAccount(result.account);
        const userInfo = await authService.handleLoginSuccess(result);
        
        if (userInfo) {
          setUser(userInfo);
          setIsAuthenticated(true);
        }
      }
    } catch (error) {
      console.error("Login failed", error);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await msalInstance.logoutPopup();
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error("Logout failed", error);
    }
  };

  const getAccessToken = async (): Promise<string | null> => {
    try {
      const account = msalInstance.getActiveAccount();
      if (!account) {
        throw new Error("No active account");
      }

      const response = await msalInstance.acquireTokenSilent({
        ...loginRequest,
        account,
      });

      return response.accessToken;
    } catch (error) {
      console.error("Error acquiring token", error);
      return null;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        login,
        logout,
        getAccessToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};