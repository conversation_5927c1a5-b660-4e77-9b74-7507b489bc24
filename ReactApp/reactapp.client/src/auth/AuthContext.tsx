import { createContext, useContext } from "react";
import { UserInfo } from "./AuthService";

export interface AuthContextProps {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserInfo | null;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  getAccessToken: () => Promise<string | null>;
}

export const AuthContext = createContext<AuthContextProps>({
  isAuthenticated: false,
  isLoading: true,
  user: null,
  login: async () => {},
  logout: async () => {},
  getAccessToken: async () => null,
});

export const useAuth = () => useContext(AuthContext);