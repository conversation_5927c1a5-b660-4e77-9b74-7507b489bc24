export const msalConfig = {
  auth: {
    clientId: "your-client-id", // Same as in appsettings.json
    authority: "https://login.microsoftonline.com/your-tenant-id", // Combine instance and tenantId
    redirectUri: window.location.origin,
    postLogoutRedirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false,
  },
};

export const loginRequest = {
  scopes: ["openid", "profile", "email", "api://your-client-id/access_as_user"],
};